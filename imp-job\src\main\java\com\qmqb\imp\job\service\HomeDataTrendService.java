package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.dingtalk.api.response.OapiAttendanceListResponse;
import com.google.common.collect.Lists;
import com.hzed.structure.common.util.str.StringUtil;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.HomeTrendEnum;
import com.qmqb.imp.common.enums.ZtStoryStatusEnum;
import com.qmqb.imp.common.enums.ZtTaskStatusEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.LocalDateTimeUtils;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.job.api.client.DingTalkOApiClient;
import com.qmqb.imp.system.domain.HomeTrend;
import com.qmqb.imp.system.domain.SysConfig;
import com.qmqb.imp.system.service.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 首页数据趋势统计
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HomeDataTrendService {

    private final ISysUserService sysUserService;

    private final ISysDeptService iSysDeptService;

    private final IZtTaskService iZtTaskService;

    private final IHomeTrendService homeTrendService;

    private final DingTalkOApiClient dingTalkOApiClient;

    private final IZtDocService iZtDocService;

    private final IProjectService iProjectService;

    private final ISysConfigService iSysConfigService;

    private final IStoryService iStoryService;

    /**
     * 首页团队数据趋势统计定时任务
     */
    @TraceId("首页团队趋势统计定时任务")
    @XxlJob("homeTrendTeamStatJobHandler")
    public ReturnT<String> homeTrendTeamStatJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行首页团队趋势统计定时任务...");
            log.info("开始执行首页团队趋势统计定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();

            List<LocalDateTime> startTimeList = Lists.newArrayList();
            List<LocalDateTime> endTimeList = Lists.newArrayList();
            LocalDateTime monthStartTime;
            LocalDateTime monthEndTime;
            if (StringUtils.isNotBlank(param)) {
                String[] params = param.split(",");
                for (String date : params) {
                    LocalDateTime parseLocalDateTime = DateUtil.parseLocalDateTime(date);
                    // 获取当前日期时间的年份和月份
                    YearMonth yearMonth = YearMonth.from(parseLocalDateTime);
                    // 获取开始时间（该月的第一天）
                    monthStartTime = yearMonth.atDay(1).atStartOfDay();
                    // 获取结束时间（该月的最后一天）
                    monthEndTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
                    startTimeList.add(monthStartTime);
                    endTimeList.add(monthEndTime);
                }
            } else {
                monthStartTime = LocalDateTimeUtils.monthStartTime();
                monthEndTime = LocalDateTimeUtils.monthEndTime().truncatedTo(ChronoUnit.SECONDS);
                startTimeList.add(monthStartTime);
                endTimeList.add(monthEndTime);
            }
            List<HomeTrend> homeTrendList = new ArrayList<>();
            for (int i = 0; i < startTimeList.size(); i++) {
                monthStartTime = startTimeList.get(i);
                monthEndTime = endTimeList.get(i);
                if (monthStartTime != null && monthEndTime != null) {
                    HomeTrend homeTrend = new HomeTrend();
                    //按月统计，记录当前月第一天
                    homeTrend.setTrendTime(DateUtils.toDate(startTimeList.get(i)));
                    //新增团队人数趋势(sys_role(开发:开发人员+运维人员 测试:测试人员 项管:项目经理) sys_dept:leader组长)
                    Integer developerCount = sysUserService.selectUserCountByRoleName("开发人员", DateUtils.toDate(monthEndTime));
                    Integer omCount = sysUserService.selectUserCountByRoleName("运维人员", DateUtils.toDate(monthEndTime));
                    homeTrend.setDeveloperCount(Long.valueOf(developerCount + omCount));
                    Integer testerCount = sysUserService.selectUserCountByRoleName("测试人员", DateUtils.toDate(monthEndTime));
                    homeTrend.setTesterCount(Long.valueOf(testerCount));
                    Integer pmCount = sysUserService.selectUserCountByRoleName("项目经理", DateUtils.toDate(monthEndTime));
                    homeTrend.setPmCount(Long.valueOf(pmCount));
                    Integer leaderCount = iSysDeptService.selectLeaderCount();
                    homeTrend.setLeaderCount(Long.valueOf(leaderCount));
                    homeTrendList.add(homeTrend);
                }
            }

            homeTrendService.insertOrUpdateBatch(homeTrendList);
            insertOrUpdateOperateTime(new Date(), HomeTrendEnum.TEAM.value);
            sw.stop();
            XxlJobLogger.log("首页团队趋势统计定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("首页团队趋势统计定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("首页团队趋势统计定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 首页文档趋势统计定时任务
     */
    @TraceId("首页文档趋势统计定时任务")
    @XxlJob("homeTrendDocStatJobHandler")
    public ReturnT<String> homeTrendDocStatJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行首页文档趋势统计定时任务...");
            log.info("开始执行首页文档趋势统计定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();

            List<LocalDateTime> startTimeList = Lists.newArrayList();
            List<LocalDateTime> endTimeList = Lists.newArrayList();
            LocalDateTime monthStartTime;
            LocalDateTime monthEndTime;
            if (StringUtils.isNotBlank(param)) {
                String[] params = param.split(",");
                for (String date : params) {
                    LocalDateTime parseLocalDateTime = DateUtil.parseLocalDateTime(date);
                    // 获取当前日期时间的年份和月份
                    YearMonth yearMonth = YearMonth.from(parseLocalDateTime);
                    // 获取开始时间（该月的第一天）
                    monthStartTime = yearMonth.atDay(1).atStartOfDay();
                    // 获取结束时间（该月的最后一天）
                    monthEndTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
                    startTimeList.add(monthStartTime);
                    endTimeList.add(monthEndTime);
                }
            } else {
                monthStartTime = LocalDateTimeUtils.monthStartTime();
                monthEndTime = LocalDateTimeUtils.monthEndTime().truncatedTo(ChronoUnit.SECONDS);
                startTimeList.add(monthStartTime);
                endTimeList.add(monthEndTime);
            }
            List<HomeTrend> homeTrendList = new ArrayList<>();
            for (int i = 0; i < startTimeList.size(); i++) {
                monthStartTime = startTimeList.get(i);
                monthEndTime = endTimeList.get(i);
                if (monthStartTime != null && monthEndTime != null) {
                    HomeTrend homeTrend = new HomeTrend();
                    //按月统计，记录当前月第一天
                    homeTrend.setTrendTime(DateUtils.toDate(startTimeList.get(i)));
                    //新增文档趋势(zt_doc)
                    Long docCount = iZtDocService.selectDocCountByAddedDate(DateUtils.toDate(monthStartTime), DateUtils.toDate(monthEndTime));
                    homeTrend.setDocCount(docCount);
                    homeTrendList.add(homeTrend);
                }
            }

            homeTrendService.insertOrUpdateBatch(homeTrendList);
            insertOrUpdateOperateTime(new Date(), HomeTrendEnum.DOC.value);
            sw.stop();
            XxlJobLogger.log("首页文档趋势统计定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("首页文档趋势统计定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("首页文档趋势统计定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 首页代码库趋势统计定时任务
     */
    @TraceId("首页代码库趋势统计定时任务")
    @XxlJob("homeTrendProjectStatJobHandler")
    public ReturnT<String> homeTrendProjectStatJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行首页代码库趋势统计定时任务...");
            log.info("开始执行首页代码库趋势统计定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();

            List<LocalDateTime> startTimeList = Lists.newArrayList();
            List<LocalDateTime> endTimeList = Lists.newArrayList();
            LocalDateTime monthStartTime;
            LocalDateTime monthEndTime;
            if (StringUtils.isNotBlank(param)) {
                String[] params = param.split(",");
                for (String date : params) {
                    LocalDateTime parseLocalDateTime = DateUtil.parseLocalDateTime(date);
                    // 获取当前日期时间的年份和月份
                    YearMonth yearMonth = YearMonth.from(parseLocalDateTime);
                    // 获取开始时间（该月的第一天）
                    monthStartTime = yearMonth.atDay(1).atStartOfDay();
                    // 获取结束时间（该月的最后一天）
                    monthEndTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
                    startTimeList.add(monthStartTime);
                    endTimeList.add(monthEndTime);
                }
            } else {
                monthStartTime = LocalDateTimeUtils.monthStartTime();
                monthEndTime = LocalDateTimeUtils.monthEndTime().truncatedTo(ChronoUnit.SECONDS);
                startTimeList.add(monthStartTime);
                endTimeList.add(monthEndTime);
            }
            List<HomeTrend> homeTrendList = new ArrayList<>();
            for (int i = 0; i < startTimeList.size(); i++) {
                monthStartTime = startTimeList.get(i);
                monthEndTime = endTimeList.get(i);
                if (monthStartTime != null && monthEndTime != null) {
                    HomeTrend homeTrend = new HomeTrend();
                    //按月统计，记录当前月第一天
                    homeTrend.setTrendTime(DateUtils.toDate(startTimeList.get(i)));
                    //新增代码库趋势(tb_project)
                    Long gitProjectCount = iProjectService.selectProjectsCountByCreateTime(DateUtils.toDate(monthStartTime), DateUtils.toDate(monthEndTime));
                    homeTrend.setGitProjectCount(gitProjectCount);
                    homeTrendList.add(homeTrend);
                }
            }

            homeTrendService.insertOrUpdateBatch(homeTrendList);
            insertOrUpdateOperateTime(new Date(), HomeTrendEnum.PROJECT.value);
            sw.stop();
            XxlJobLogger.log("首页代码库趋势统计定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("首页代码库趋势统计定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("首页代码库趋势统计定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 首页任务趋势统计定时任务，要统计一年，覆盖旧数据
     *
     * @param param
     * @return
     */
    @TraceId("首页任务趋势统计定时任务")
    @XxlJob("homeTrendTaskStatJobHandler")
    public ReturnT<String> homeTrendTaskStatJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行首页任务趋势统计定时任务...");
            log.info("开始执行首页任务趋势统计定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();

            List<LocalDateTime> startTimeList = Lists.newArrayList();
            List<LocalDateTime> endTimeList = Lists.newArrayList();
            LocalDateTime monthStartTime;
            LocalDateTime monthEndTime;
            if (StringUtils.isNotBlank(param)) {
                String[] params = param.split(",");
                for (String date : params) {
                    LocalDateTime parseLocalDateTime = DateUtil.parseLocalDateTime(date);
                    // 获取当前日期时间的年份和月份
                    YearMonth yearMonth = YearMonth.from(parseLocalDateTime);
                    // 获取开始时间（该月的第一天）
                    monthStartTime = yearMonth.atDay(1).atStartOfDay();
                    // 获取结束时间（该月的最后一天）
                    monthEndTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
                    startTimeList.add(monthStartTime);
                    endTimeList.add(monthEndTime);
                }
            } else {
                //无传参统计一年，覆盖旧数据
                monthStartTime = LocalDateTimeUtils.monthStartTime();
                monthEndTime = LocalDateTimeUtils.monthEndTime().truncatedTo(ChronoUnit.SECONDS);
                // 从当前月份开始循环，直到年初，获取年初日期（1月1日）
                LocalDateTime yearStart = monthStartTime.withMonth(1).withDayOfMonth(1);
                while (!monthStartTime.isBefore(yearStart)) {
                    startTimeList.add(monthStartTime);
                    endTimeList.add(monthEndTime);
                    // 移动到上一个月
                    monthStartTime = monthStartTime.minusMonths(1);
                    monthEndTime = monthStartTime.plusMonths(1).minusSeconds(1);
                }
            }

            List<String> usernameList = sysUserService.selectAllUser().stream().map(SysUser::getZtUserName).collect(Collectors.toList());
            List<HomeTrend> homeTrendList = new ArrayList<>();
            for (int i = 0; i < startTimeList.size(); i++) {
                monthStartTime = startTimeList.get(i);
                monthEndTime = endTimeList.get(i);
                if (monthStartTime != null && monthEndTime != null) {
                    HomeTrend homeTrend = new HomeTrend();
                    //按月统计，记录当前月第一天
                    homeTrend.setTrendTime(DateUtils.toDate(startTimeList.get(i)));
                    Long taskWait = iStoryService.selectStoryCountByStatus(DateUtils.toDate(monthStartTime), DateUtils.toDate(monthEndTime), ZtStoryStatusEnum.ACTIVE,usernameList);
                    Long taskDoing = iZtTaskService.selectTaskCountByStatus(DateUtils.toDate(monthStartTime), DateUtils.toDate(monthEndTime), ZtTaskStatusEnum.DOING, usernameList);
                    Long taskDone = iZtTaskService.selectTaskCountByStatus(DateUtils.toDate(monthStartTime), DateUtils.toDate(monthEndTime), ZtTaskStatusEnum.DONE, usernameList);
                    Long taskCancel = iZtTaskService.selectTaskCountByStatus(DateUtils.toDate(monthStartTime), DateUtils.toDate(monthEndTime), ZtTaskStatusEnum.CANCEL, usernameList);
                    Long taskClosed = iZtTaskService.selectTaskCountByStatus(DateUtils.toDate(monthStartTime), DateUtils.toDate(monthEndTime), ZtTaskStatusEnum.CLOSED, usernameList);
                    Long taskPause = iZtTaskService.selectTaskCountByStatus(DateUtils.toDate(monthStartTime), DateUtils.toDate(monthEndTime), ZtTaskStatusEnum.PAUSE, usernameList);
                    homeTrend.setTaskWait(taskWait);
                    homeTrend.setTaskDoing(taskDoing);
                    homeTrend.setTaskDone(taskDone);
                    homeTrend.setTaskClosed(taskClosed);
                    homeTrend.setTaskCancel(taskCancel);
                    homeTrend.setTaskPause(taskPause);
                    homeTrendList.add(homeTrend);
                }
            }

            homeTrendService.insertOrUpdateBatch(homeTrendList);
            insertOrUpdateOperateTime(new Date(), HomeTrendEnum.TASK.value);
            sw.stop();
            XxlJobLogger.log("首页任务趋势统计定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("首页任务趋势统计定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("首页任务趋势统计定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 首页考勤晚上20点后打卡的人数统计定时任务
     *
     * @param param
     * @return
     */
    @TraceId("首页考勤晚上20点后打卡的人数统计定时任务")
    @XxlJob("KqAfter20StatJobHandler")
    public ReturnT<String> kqAfter20StatJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行首页考勤晚上20点后打卡的人数统计定时任务...");
            log.info("开始执行首页考勤晚上20点后打卡的人数统计定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();

            List<String> startTimeList = Lists.newArrayList();
            List<String> endTimeList = Lists.newArrayList();
            String todayStartTime;
            String todayEndTime;
            if (StringUtils.isNotBlank(param)) {
                String[] params = param.split(",");
                for (String date : params) {
                    LocalDateTime parseLocalDateTime = DateUtil.parseLocalDateTime(date);
                    // 获取一天的开始时间
                    todayStartTime = DateUtil.formatLocalDateTime(parseLocalDateTime.truncatedTo(ChronoUnit.DAYS));
                    // 获取一天的结束时间
                    todayEndTime = DateUtil.formatLocalDateTime(parseLocalDateTime.toLocalDate().atTime(LocalTime.MAX));
                    startTimeList.add(todayStartTime);
                    endTimeList.add(todayEndTime);
                }
            } else {
                todayStartTime = DateUtil.formatLocalDateTime(LocalDateTimeUtils.todayStartTime());
                todayEndTime = DateUtil.formatLocalDateTime(LocalDateTimeUtils.todayEndTime().minusSeconds(1));
                startTimeList.add(todayStartTime);
                endTimeList.add(todayEndTime);
            }
            List<HomeTrend> homeTrendList = new ArrayList<>();
            //新增20点后考勤人数统计(调用钉钉考勤接口),统计一天的数据
            List<SysUser> sysUserList = sysUserService.selectAllUser();
            // 获取用户对应的钉钉id
            List<String> userDingTalkIdList = sysUserList.stream().map(SysUser::getDingtalkUserId).collect(Collectors.toList());
            Map<Date, List<OapiAttendanceListResponse.Recordresult>> collect = getRecordResultAllList(sysUserList, userDingTalkIdList, startTimeList, endTimeList)
                .stream()
                .filter(recordResult -> userDingTalkIdList.contains(recordResult.getUserId()))
                .filter(recordResult -> StringUtil.equals("OffDuty", recordResult.getCheckType()))
                .collect(Collectors.groupingBy(OapiAttendanceListResponse.Recordresult::getWorkDate));
            collect.forEach((key, value) -> {
                Long kqAfter20 = value.stream()
                    .filter(recordResult -> {
                        LocalDate workDate = key.toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                        LocalTime evening8Pm = LocalTime.of(20, 0); // 晚上8点
                        LocalDateTime evening8PmDateTime = LocalDateTime.of(workDate, evening8Pm);
                        ZonedDateTime evening8PmZonedDateTime = evening8PmDateTime.atZone(ZoneId.systemDefault());
                        // 将userCheckTime转换为ZonedDateTime
                        ZonedDateTime userCheckTime = recordResult.getUserCheckTime().toInstant()
                            .atZone(ZoneId.systemDefault());
                        // 判断userCheckTime是否在当天晚上8点及之后
                        return userCheckTime.isAfter(evening8PmZonedDateTime) || userCheckTime.isEqual(evening8PmZonedDateTime);
                    })
                    .count();
                HomeTrend homeTrend = new HomeTrend();
                homeTrend.setTrendTime(key);
                homeTrend.setKqAfter20(kqAfter20);
                homeTrendList.add(homeTrend);
            });
            //数据为空，补0
            if (CollUtil.isEmpty(collect)) {
                for (String startTime : startTimeList) {
                    HomeTrend homeTrend = new HomeTrend();
                    homeTrend.setTrendTime(DateUtil.parseDate(startTime));
                    homeTrend.setKqAfter20(0L);
                    homeTrendList.add(homeTrend);
                }
            }
            homeTrendService.insertOrUpdateBatch(homeTrendList);
            insertOrUpdateOperateTime(new Date(), HomeTrendEnum.KQAFTER20.value);
            sw.stop();
            XxlJobLogger.log("首页考勤晚上20点后打卡的人数统计定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("首页考勤晚上20点后打卡的人数统计定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("首页考勤晚上20点后打卡的人数统计定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 遍历调用钉钉考勤接口得到的结果汇总
     * @param sysUserList
     * @param userDingTalkIdList
     * @param startTimeList
     * @param endTimeList
     * @return
     */
    public List<OapiAttendanceListResponse.Recordresult> getRecordResultAllList( List<SysUser> sysUserList,List<String> userDingTalkIdList,List<String> startTimeList, List<String> endTimeList) {
        // 遍历调用钉钉考勤接口得到的结果汇总
        List<OapiAttendanceListResponse.Recordresult> recordResultAllList = Lists.newArrayList();
        for (int i = 0; i < userDingTalkIdList.size() / CommConstants.CommonVal.FIFTY + 1; i++) {
            List<String> userIdQuery;
            if (i * 50 + 49 > userDingTalkIdList.size()) {
                userIdQuery = CollUtil.sub(userDingTalkIdList, i * 50, userDingTalkIdList.size());
            } else {
                userIdQuery = CollUtil.sub(userDingTalkIdList, i * 50, (i + 1) * 50);
            }
            if (CollUtil.isNotEmpty(userIdQuery)) {
                for (int j = 0; j < startTimeList.size(); j++) {
                    Long offset = 0L;
                    Boolean hasMore = true;
                    while (hasMore) {
                        String workDateFrom = startTimeList.get(j);
                        String workDateTo = endTimeList.get(j);
                        if (StrUtil.isNotBlank(workDateFrom) && StrUtil.isNotBlank(workDateTo)) {
                            OapiAttendanceListResponse attendanceResultList = dingTalkOApiClient.getAttendanceResultList(userIdQuery, workDateFrom, workDateTo, offset, 50L);
                            List<OapiAttendanceListResponse.Recordresult> recordresult = attendanceResultList.getRecordresult();
                            if (CollUtil.isNotEmpty(recordresult)) {
                                recordResultAllList.addAll(recordresult);
                            }
                            hasMore = attendanceResultList.getHasMore();
                            offset += 50L;
                        }
                    }
                }
            }
        }
        return recordResultAllList;
    }

    /**
     * 记录每个定时器执行的时间
     *
     * @param date 执行时间
     * @param type 定时器类型
     */
    private void insertOrUpdateOperateTime(Date date, String type) {
        SysConfig sysConfig = iSysConfigService.selectConfigBoByKey("home.trend.updateTime");
        String homeTrendUpdateTime;
        Map<String, Date> updateTimeMap = new HashMap<>(5);
        if (sysConfig != null) {
            homeTrendUpdateTime = sysConfig.getConfigValue();
            updateTimeMap = JSON.parseObject(homeTrendUpdateTime, Map.class);
            updateTimeMap.put(type, date);
            SysConfig config = new SysConfig();
            config.setConfigId(sysConfig.getConfigId());
            config.setConfigKey("home.trend.updateTime");
            homeTrendUpdateTime = JSON.toJSONString(updateTimeMap);
            config.setConfigValue(homeTrendUpdateTime);
            iSysConfigService.updateConfig(config);
        } else {
            updateTimeMap.put(type, date);
            homeTrendUpdateTime = JSON.toJSONString(updateTimeMap);
            SysConfig config = new SysConfig();
            config.setConfigKey("home.trend.updateTime");
            config.setConfigValue(homeTrendUpdateTime);
            config.setRemark("定时任务执行时间记录，格式：{type:date}");
            config.setConfigName("定时任务执行时间记录");
            config.setConfigType("N");
            iSysConfigService.insertConfig(config);
        }
    }
}
