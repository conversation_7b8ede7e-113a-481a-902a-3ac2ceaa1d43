<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.process.ProcessDeptCooperationInterviewMapper">

    <resultMap type="com.qmqb.imp.system.domain.process.ProcessDeptCooperationInterview" id="ProcessDeptCooperationInterviewResult">
        <result property="id" column="id"/>
        <result property="approvalId" column="approval_id"/>
        <result property="yearValue" column="year_value"/>
        <result property="monthValue" column="month_value"/>
        <result property="type" column="type"/>
        <result property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="employeeName" column="employee_name"/>
        <result property="employeePositionType" column="employee_position_type"/>
        <result property="employeePosition" column="employee_position"/>
        <result property="description" column="description"/>
        <result property="submitterName" column="submitter_name"/>
        <result property="submitterGroupId" column="submitter_group_id"/>
        <result property="submitterGroup" column="submitter_group"/>
        <result property="approvalResult" column="approval_result"/>
        <result property="submitTime" column="submit_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <insert id="batchInsertOnUpdate">
        INSERT INTO tb_dept_cooperation_interview(
            `approval_id`, `year_value`, `month_value`, `type`, `group_id`, `group_name`,
            `employee_name`, `employee_position_type`, `employee_position`, `description`,
            `submitter_name`, `submitter_group_id`, `submitter_group`, `approval_result`,
            `submit_time`, `del_flag`
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.approvalId}, #{item.yearValue}, #{item.monthValue}, #{item.type},
                #{item.groupId}, #{item.groupName}, #{item.employeeName},
                #{item.employeePositionType}, #{item.employeePosition}, #{item.description},
                #{item.submitterName}, #{item.submitterGroupId}, #{item.submitterGroup},
                #{item.approvalResult}, #{item.submitTime}, #{item.delFlag}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            `year_value` = VALUES(`year_value`),
            `month_value` = VALUES(`month_value`),
            `type` = VALUES(`type`),
            `approval_id` = VALUES(`approval_id`),
            `employee_name` = VALUES(`employee_name`)
    </insert>

    <!-- 获取基础统计数据 -->
    <select id="getBasicStatistics" resultType="com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewStatisticsVo$BasicStatistics">
        SELECT
            COUNT(DISTINCT approval_id) as submitCount,
            SUM(CASE WHEN type = '1' THEN 1 ELSE 0 END) as praiseCount,
            SUM(CASE WHEN type = '0' THEN 1 ELSE 0 END) as improvementCount
        FROM tb_dept_cooperation_interview
        WHERE del_flag = 0
          AND year_value = #{yearValue}
          AND group_id IS NOT NULL
          <if test="monthValue != null">
              AND month_value = #{monthValue}
          </if>
    </select>

    <!-- 获取个人被表扬次数TOP10 -->
    <select id="getPraiseTop10" resultType="com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewStatisticsVo$PersonalRanking">
        SELECT
            (@row_number := @row_number + 1) as ranking,
            employeeName,
            count
        FROM (
            SELECT
                employee_name as employeeName,
                COUNT(*) as count
            FROM tb_dept_cooperation_interview
            WHERE del_flag = 0
              AND type = '1'
              AND year_value = #{yearValue}
              AND group_id IS NOT NULL
              <if test="monthValue != null">
                  AND month_value = #{monthValue}
              </if>
            GROUP BY employee_name
            ORDER BY count DESC
            LIMIT 10
        ) t, (SELECT @row_number := 0) r
    </select>

    <!-- 获取个人待改进次数TOP10 -->
    <select id="getImprovementTop10" resultType="com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewStatisticsVo$PersonalRanking">
        SELECT
            (@row_number := @row_number + 1) as ranking,
            employeeName,
            count
        FROM (
            SELECT
                employee_name as employeeName,
                COUNT(*) as count
            FROM tb_dept_cooperation_interview
            WHERE del_flag = 0
              AND type = '0'
              AND year_value = #{yearValue}
              AND group_id IS NOT NULL
              <if test="monthValue != null">
                  AND month_value = #{monthValue}
              </if>
            GROUP BY employee_name
            ORDER BY count DESC
            LIMIT 10
        ) t, (SELECT @row_number := 0) r
    </select>

    <!-- 获取按月统计的时间趋势数据 -->
    <select id="getMonthlyTimeTrends" resultType="com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewStatisticsVo$TimeTrend">
        SELECT
            CONCAT(month_value, '月') as timeLabel,
            SUM(CASE WHEN type = '1' THEN 1 ELSE 0 END) as praiseCount,
            SUM(CASE WHEN type = '0' THEN 1 ELSE 0 END) as improvementCount
        FROM tb_dept_cooperation_interview
        WHERE del_flag = 0
          AND year_value = #{yearValue}
          AND group_id IS NOT NULL
        GROUP BY month_value
        ORDER BY month_value
    </select>

    <!-- 获取按天统计的时间趋势数据 -->
    <select id="getDailyTimeTrends" resultType="com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewStatisticsVo$TimeTrend">
        SELECT
            DAY(submit_time) as timeLabel,
            SUM(CASE WHEN type = '1' THEN 1 ELSE 0 END) as praiseCount,
            SUM(CASE WHEN type = '0' THEN 1 ELSE 0 END) as improvementCount
        FROM tb_dept_cooperation_interview
        WHERE del_flag = 0
          AND year_value = #{yearValue}
          AND month_value = #{monthValue}
          AND group_id IS NOT NULL
        GROUP BY DAY(submit_time)
        ORDER BY DAY(submit_time)
    </select>

    <!-- 获取按组统计数据 -->
    <select id="getGroupStatistics" resultType="com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewStatisticsVo$GroupStatistics">
        SELECT
            group_name as groupName,
            SUM(CASE WHEN type = '1' THEN 1 ELSE 0 END) as praiseCount,
            SUM(CASE WHEN type = '0' THEN 1 ELSE 0 END) as improvementCount
        FROM tb_dept_cooperation_interview
        WHERE del_flag = 0
          AND year_value = #{yearValue}
          <if test="monthValue != null">
              AND month_value = #{monthValue}
          </if>
          AND group_id IS NOT NULL
          AND group_name IS NOT NULL
        GROUP BY group_name
        ORDER BY (SUM(CASE WHEN type = '1' THEN 1 ELSE 0 END) + SUM(CASE WHEN type = '0' THEN 1 ELSE 0 END)) DESC
    </select>

    <!-- 获取按岗位统计数据 -->
    <select id="getPositionStatistics" resultType="com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewStatisticsVo$PositionStatistics">
        SELECT
            employee_position as positionName,
            SUM(CASE WHEN type = '1' THEN 1 ELSE 0 END) as praiseCount,
            SUM(CASE WHEN type = '0' THEN 1 ELSE 0 END) as improvementCount
        FROM tb_dept_cooperation_interview
        WHERE del_flag = 0
          AND year_value = #{yearValue}
          <if test="monthValue != null">
              AND month_value = #{monthValue}
          </if>
          AND group_id IS NOT NULL
          AND employee_position IS NOT NULL
        GROUP BY employee_position
        ORDER BY (SUM(CASE WHEN type = '1' THEN 1 ELSE 0 END) + SUM(CASE WHEN type = '0' THEN 1 ELSE 0 END)) DESC
    </select>

</mapper>
