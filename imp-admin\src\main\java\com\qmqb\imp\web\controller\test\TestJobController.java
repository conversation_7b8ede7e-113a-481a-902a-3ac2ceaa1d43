package com.qmqb.imp.web.controller.test;


import cn.dev33.satoken.annotation.SaIgnore;
import com.qmqb.imp.job.service.SyncDeptCooperationInterviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 11:49 2025/9/5
 * @Description TODO
 * @MethodName
 * @return null
 */
@RestController
@RequestMapping("/test")
public class TestJobController {

    @Autowired
    private SyncDeptCooperationInterviewService syncDeptCooperationInterviewService;

    @SaIgnore
    @GetMapping
    public void test(String param){
        syncDeptCooperationInterviewService.syncDeptCooperationInterviewJobHandler(param);
    }
}
