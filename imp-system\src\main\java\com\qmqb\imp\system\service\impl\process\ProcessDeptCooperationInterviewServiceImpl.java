package com.qmqb.imp.system.service.impl.process;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.bo.process.ProcessDeptCooperationInterviewBo;
import com.qmqb.imp.system.domain.bo.process.ProcessDeptCooperationInterviewStatisticsBo;
import com.qmqb.imp.system.domain.process.ProcessDeptCooperationInterview;
import com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewVo;
import com.qmqb.imp.system.domain.vo.process.ProcessDeptCooperationInterviewStatisticsVo;
import com.qmqb.imp.system.mapper.process.ProcessDeptCooperationInterviewMapper;
import com.qmqb.imp.system.service.process.IProcessDeptCooperationInterviewService;
import com.qmqb.imp.system.service.ISysConfigService;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 部门合作度访谈Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@RequiredArgsConstructor
@Service
public class ProcessDeptCooperationInterviewServiceImpl implements IProcessDeptCooperationInterviewService {

    private final ProcessDeptCooperationInterviewMapper baseMapper;
    private final ISysConfigService iSysConfigService;

    /**
     * 查询部门合作度访谈
     */
    @Override
    public ProcessDeptCooperationInterviewVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询部门合作度访谈列表
     */
    @Override
    public TableDataInfo<ProcessDeptCooperationInterviewVo> queryPageList(ProcessDeptCooperationInterviewBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProcessDeptCooperationInterview> lqw = buildQueryWrapper(bo);
        Page<ProcessDeptCooperationInterviewVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询部门合作度访谈列表
     */
    @Override
    public List<ProcessDeptCooperationInterviewVo> queryList(ProcessDeptCooperationInterviewBo bo) {
        LambdaQueryWrapper<ProcessDeptCooperationInterview> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 获取部门合作度访谈统计数据
     */
    @Override
    public ProcessDeptCooperationInterviewStatisticsVo getStatistics(ProcessDeptCooperationInterviewStatisticsBo bo) {
        ProcessDeptCooperationInterviewStatisticsVo result = new ProcessDeptCooperationInterviewStatisticsVo();

        // 获取基础统计数据
        ProcessDeptCooperationInterviewStatisticsVo.BasicStatistics basicStats =
            baseMapper.getBasicStatistics(bo.getYearValue(), bo.getMonthValue());
        result.setBasicStatistics(basicStats);

        // 获取个人被表扬次数TOP10
        List<ProcessDeptCooperationInterviewStatisticsVo.PersonalRanking> praiseTop10 =
            baseMapper.getPraiseTop10(bo.getYearValue(), bo.getMonthValue());
        result.setPraiseTop10(praiseTop10);

        // 获取个人待改进次数TOP10
        List<ProcessDeptCooperationInterviewStatisticsVo.PersonalRanking> improvementTop10 =
            baseMapper.getImprovementTop10(bo.getYearValue(), bo.getMonthValue());
        result.setImprovementTop10(improvementTop10);

        // 获取时间趋势数据
        List<ProcessDeptCooperationInterviewStatisticsVo.TimeTrend> timeTrends;
        if (bo.getMonthValue() != null) {
            // 如果传了月份，按天统计
            timeTrends = baseMapper.getDailyTimeTrends(bo.getYearValue(), bo.getMonthValue());
        } else {
            // 如果只传了年份，按月统计
            timeTrends = baseMapper.getMonthlyTimeTrends(bo.getYearValue());
        }
        result.setTimeTrends(timeTrends);

        // 获取按组统计数据
        List<ProcessDeptCooperationInterviewStatisticsVo.GroupStatistics> groupStats =
            baseMapper.getGroupStatistics(bo.getYearValue(), bo.getMonthValue());
        result.setGroupStatistics(groupStats);

        // 获取按岗位统计数据
        List<ProcessDeptCooperationInterviewStatisticsVo.PositionStatistics> positionStats =
            baseMapper.getPositionStatistics(bo.getYearValue(), bo.getMonthValue());
        result.setPositionStatistics(positionStats);

        // 获取定时器更新时间
        Date updateTime = getJobUpdateTime();
        result.setUpdateTime(updateTime);

        return result;
    }

    private LambdaQueryWrapper<ProcessDeptCooperationInterview> buildQueryWrapper(ProcessDeptCooperationInterviewBo bo) {
        LambdaQueryWrapper<ProcessDeptCooperationInterview> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApprovalId()), ProcessDeptCooperationInterview::getApprovalId, bo.getApprovalId());
        lqw.eq(bo.getYearValue() != null, ProcessDeptCooperationInterview::getYearValue, bo.getYearValue());
        lqw.eq(bo.getMonthValue() != null, ProcessDeptCooperationInterview::getMonthValue, bo.getMonthValue());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ProcessDeptCooperationInterview::getType, bo.getType());
        lqw.eq(bo.getGroupId() != null, ProcessDeptCooperationInterview::getGroupId, bo.getGroupId());
        lqw.like(StringUtils.isNotBlank(bo.getGroupName()), ProcessDeptCooperationInterview::getGroupName, bo.getGroupName());
        lqw.like(StringUtils.isNotBlank(bo.getEmployeeName()), ProcessDeptCooperationInterview::getEmployeeName, bo.getEmployeeName());
        lqw.eq(StringUtils.isNotBlank(bo.getEmployeePositionType()), ProcessDeptCooperationInterview::getEmployeePositionType, bo.getEmployeePositionType());
        lqw.like(StringUtils.isNotBlank(bo.getEmployeePosition()), ProcessDeptCooperationInterview::getEmployeePosition, bo.getEmployeePosition());
        lqw.like(StringUtils.isNotBlank(bo.getDescription()), ProcessDeptCooperationInterview::getDescription, bo.getDescription());
        lqw.like(StringUtils.isNotBlank(bo.getSubmitterName()), ProcessDeptCooperationInterview::getSubmitterName, bo.getSubmitterName());
        lqw.eq(bo.getSubmitterGroupId() != null, ProcessDeptCooperationInterview::getSubmitterGroupId, bo.getSubmitterGroupId());
        lqw.like(StringUtils.isNotBlank(bo.getSubmitterGroup()), ProcessDeptCooperationInterview::getSubmitterGroup, bo.getSubmitterGroup());
        lqw.eq(StringUtils.isNotBlank(bo.getApprovalResult()), ProcessDeptCooperationInterview::getApprovalResult, bo.getApprovalResult());
        lqw.between(bo.getSubmitTimeStart() != null && bo.getSubmitTimeEnd() != null,
            ProcessDeptCooperationInterview::getSubmitTime, bo.getSubmitTimeStart(), bo.getSubmitTimeEnd());
        lqw.orderByDesc(ProcessDeptCooperationInterview::getSubmitTime);
        return lqw;
    }

    /**
     * 获取定时器更新时间
     *
     * @return 更新时间
     */
    private Date getJobUpdateTime() {
        Date updateTime = null;
        // 查询部门合作度访谈定时任务的更新时间
        String configValue = iSysConfigService.selectConfigByKey("dept.cooperation.interview.updateTime");
        if (StrUtil.isNotBlank(configValue)) {
            try {
                Long timestamp = Long.valueOf(configValue);
                updateTime = new Date(timestamp);
            } catch (NumberFormatException e) {
                // 如果配置值不是有效的时间戳，返回null
                updateTime = null;
            }
        }
        return updateTime;
    }

}
